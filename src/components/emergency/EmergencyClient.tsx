'use client';

import { <PERSON>aP<PERSON>, FaWhatsapp, FaClock, FaShieldAlt, FaTools, FaWater, FaWrench, FaBuilding } from "react-icons/fa";
import Link from "next/link";
import Image from "next/image";
import { Fade } from "@/components/animations/Fade";
import { EmergencyContactForm } from "@/components/forms/EmergencyContactForm";
import { Translations } from "@/types/translations";
import { useState, useEffect } from "react";

interface EmergencyClientProps {
  data: any;
  t: Translations;
  lang: string;
}

export function EmergencyClient({ data, t, lang }: EmergencyClientProps) {
  // Preveniamo errori di idratazione con un hook di mounting
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Rimuoviamo il return vuoto per permettere il rendering server-side
  // Il contenuto verrà renderizzato sia lato server che lato client
  
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section - Design moderno con gradienti */}
      <section className="relative h-[80vh] min-h-[500px] overflow-hidden bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 pt-16 flex items-center">
        {/* Pattern di sfondo */}
        <div className="absolute inset-0 overflow-hidden opacity-10">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCI+CjxyZWN0IHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0ibm9uZSI+PC9yZWN0Pgo8cGF0aCBkPSJNMzAgMEMxMy40IDAgMCAxMy40IDAgMzBzMTMuNCAzMCAzMCAzMCAzMC0xMy40IDMwLTMwUzQ2LjYgMCAzMCAwem0wIDUyLjVjLTEyLjQgMC0yMi41LTEwLjEtMjIuNS0yMi41UzE3LjYgNy41IDMwIDcuNXMyMi41IDEwLjEgMjIuNSAyMi41LTEwLjEgMjIuNS0yMi41IDIyLjV6IiBmaWxsPSIjZmZmIj48L3BhdGg+Cjwvc3ZnPg==')]"></div>
        </div>
        
        {/* Elementi decorativi animati */}
        <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"></div>
        <div className="absolute bottom-40 right-20 w-32 h-32 rounded-full bg-blue-300 opacity-10 animate-pulse animation-delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-blue-400 opacity-5 animate-pulse animation-delay-2000"></div>
        
        {/* Elemento di design - forma geometrica */}
        <div className="absolute -bottom-20 -left-20 w-80 h-80 rounded-full border-8 border-white opacity-5 transform rotate-45"></div>
        <div className="absolute -top-10 -right-10 w-60 h-60 border-4 border-white opacity-5"></div>
        
        {/* Linea decorativa */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
        
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8 h-full flex flex-col justify-center z-10">
          <div className="mx-auto max-w-3xl text-center">
            <Fade>
              <div className="mb-6 inline-flex items-center justify-center rounded-full bg-blue-900 bg-opacity-20 px-4 py-1 text-sm font-medium text-white ring-1 ring-inset ring-white/20">
                <span className="mr-2 h-2 w-2 rounded-full bg-blue-400 animate-pulse"></span>
                {t.common?.emergency || 'Notfall'} 24/7
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-manrope font-bold tracking-tight text-white drop-shadow-md px-2">
                {data.hero.title}
              </h1>
              <div className="h-1 w-24 mx-auto bg-white opacity-50 my-6 rounded-full"></div>
              <p className="mt-6 text-base md:text-lg font-sans leading-relaxed text-white text-opacity-90 px-4 sm:px-0">
                {data.hero.subtitle}
              </p>
            </Fade>
            <Fade>
              <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-x-6">
                <Link
                  href={`tel:${data.cta.emergency_number}`}
                  className="w-full sm:w-auto rounded-xl bg-white px-8 py-4 text-xl font-semibold text-blue-700 shadow-lg hover:bg-blue-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                >
                  <span className="absolute inset-0 bg-blue-100 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></span>
                  <FaPhone className="h-5 w-5" />
                  <span>{data.cta.emergency_number}</span>
                </Link>
                <Link
                  href={`https://wa.me/${data.cta.whatsapp_number.replace(/\s+/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full sm:w-auto rounded-xl bg-white bg-opacity-10 border border-white border-opacity-20 backdrop-blur-sm px-8 py-4 text-xl font-semibold text-white shadow-lg hover:bg-opacity-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105"
                >
                  <FaWhatsapp className="h-5 w-5" />
                  <span>WhatsApp</span>
                </Link>
              </div>
            </Fade>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <Fade>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {data.features.title}
              </h2>
            </div>
          </Fade>
          <div className="mx-auto mt-16 max-w-7xl">
            <dl className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaClock className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature1.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature1.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaShieldAlt className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature2.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature2.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaTools className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature3.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature3.description}
                  </dd>
                </div>
              </Fade>
            </dl>
          </div>
        </div>
      </section>

      {/* Services Section - Design Moderno */}
      <section className="bg-gradient-to-b from-white to-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-10 right-10 w-32 h-32 bg-blue-50 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute -bottom-10 left-10 w-40 h-40 bg-blue-50 rounded-full opacity-70 blur-3xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16">

              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {lang === 'fr' ? (t.emergency?.services?.title || 'Nos Services d\'Urgence') : 
                   lang === 'de' ? (t.emergency?.services?.title || 'Unsere Notdienste') : 
                   (t.emergency?.services?.title || 'I Nostri Servizi di Emergenza')}
              </h2>
              <div className="h-1 w-24 mx-auto bg-blue-500 opacity-70 my-6 rounded-full"></div>
            </div>
          </Fade>

          <div className="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {data.services.map((service: any) => (
              <Fade key={service.id}>
                <div className="relative group overflow-hidden rounded-2xl bg-white ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  {/* Elementi decorativi */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-100 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  
                  <div className="p-8">
                    <div className="flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 mr-4">
                        {service.id === 1 && <FaWater className="h-6 w-6 text-blue-600" />}
                        {service.id === 2 && <FaWrench className="h-6 w-6 text-blue-600" />}
                        {service.id === 3 && <FaBuilding className="h-6 w-6 text-blue-600" />}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">{service.title}</h3>
                    </div>
                    
                    <p className="mt-4 text-sm text-gray-600">{service.description}</p>

                  </div>
                </div>
              </Fade>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16">

              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {lang === 'it' ? 'Come Gestiamo le Emergenze' : lang === 'fr' ? 'Comment Nous Gérons les Urgences' : 'Wie wir Notfälle handhaben'}
              </h2>
              <p className="mt-6 text-base leading-8 text-gray-600">
                {lang === 'it' ? 'Un processo chiaro e veloce per rispondere alle tue emergenze nel minor tempo possibile.' : 
                  lang === 'fr' ? 'Un processus clair et rapide pour répondre à vos urgences dans les plus brefs délais.' : 
                  'Ein klarer und schneller Prozess, um auf Ihre Notfälle in kürzester Zeit zu reagieren.'}
              </p>
            </div>
          </Fade>

          <div className="mx-auto mt-16 max-w-3xl">
            <ol className="relative border-l border-blue-200 space-y-16 mx-auto">
              {data.process.map((step: any) => (
                <Fade key={step.step}>
                  <li className="ml-8">
                    <div className="absolute -left-6 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-600 to-blue-500 shadow-md">
                      <span className="text-xl font-semibold text-white">{step.step}</span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                    <p className="mt-2 text-sm leading-6 text-gray-600">{step.description}</p>
                  </li>
                </Fade>
              ))}
            </ol>
          </div>
        </div>
      </section>

      {/* Trotec Products Section */}
      <section className="py-24 sm:py-32 bg-gradient-to-br from-white via-gray-50 to-blue-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-20 right-0 w-72 h-72 bg-blue-100 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-blue-100 rounded-full opacity-60 blur-3xl"></div>
          <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-yellow-50 rounded-full opacity-40 blur-2xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16">
              <div className="inline-block bg-blue-50 px-6 py-2 rounded-full mb-3">
                <span className="text-sm font-semibold text-blue-700">{lang === 'it' ? 'Soluzioni Professionali' : lang === 'fr' ? 'Solutions Professionnelles' : 'Professionelle Lösungen'}</span>
              </div>
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl relative inline-block">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-900">{data.trotec.title}</span>
                <div className="absolute -bottom-3 left-0 right-0 h-2 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full"></div>
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-700 max-w-xl mx-auto">
                {data.trotec.description}
              </p>
            </div>
          </Fade>

          <div className="mt-16 space-y-20">
            {data.trotec.products.map((product: any, index: number) => (
              <Fade key={product.id}>
                <div className="relative flex flex-col lg:flex-row items-center gap-6 bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border-b-3 border-blue-500" style={{flexDirection: index % 2 === 0 ? 'row' : 'row-reverse'}}>
                  
                  {/* Sfondo decorativo */}
                  <div className="absolute top-0 right-0 -mt-6 -mr-6 w-24 h-24 bg-gradient-to-br from-blue-200 to-blue-200 rounded-full opacity-70 blur-xl"></div>
                  <div className="absolute bottom-0 left-0 -mb-3 -ml-3 w-16 h-16 bg-gradient-to-br from-yellow-100 to-blue-100 rounded-full opacity-60 blur-xl"></div>
                  
                  <div className="w-full lg:w-1/2 z-10">
                    <div className="flex items-center mb-3">
                      <span className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-700 to-blue-500 text-white text-lg font-bold rounded-lg mr-3 shadow-md">{product.id}</span>
                      <h3 className="text-2xl font-bold text-gray-900">{product.title}</h3>
                    </div>
                    <p className="text-gray-700 text-base mb-5 leading-relaxed">{product.description}</p>
                    <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
                  </div>
                  
                  <div className="w-full lg:w-1/2 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-5 shadow-md border border-blue-100">
                    {product.id === 1 && (
                      <div className="flex flex-col items-center">
                        <div className="w-28 h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaWater className="w-14 h-14 text-blue-700" />
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-gray-900 text-xl mb-3">TWP 11025 E</div>
                          <div className="grid grid-cols-2 gap-3">
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="text-xs font-semibold text-gray-600">{lang === 'it' ? 'Portata' : lang === 'fr' ? 'Débit' : 'Durchfluss'}</div>
                              <div className="text-blue-700 font-bold text-sm">15.000 l/h</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="text-xs font-semibold text-gray-600">{lang === 'it' ? 'Potenza' : lang === 'fr' ? 'Puissance' : 'Leistung'}</div>
                              <div className="text-blue-600 font-bold text-sm">1.100 W</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="text-xs font-semibold text-gray-600">{lang === 'it' ? 'Profondità' : lang === 'fr' ? 'Profondeur' : 'Tiefe'}</div>
                              <div className="text-blue-700 font-bold text-sm">7 {lang === 'it' ? 'metri' : lang === 'fr' ? 'mètres' : 'Meter'}</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                              <div className="text-xs font-semibold text-gray-600">{lang === 'it' ? 'Mandata' : lang === 'fr' ? 'Hauteur de refoulement' : 'Förderhöhe'}</div>
                              <div className="text-blue-600 font-bold text-sm">11 {lang === 'it' ? 'metri' : lang === 'fr' ? 'mètres' : 'Meter'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {product.id === 2 && (
                      <div className="flex flex-col items-center">
                        <div className="w-28 h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaTools className="w-14 h-14 text-blue-700" />
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-gray-900 text-xl mb-3">MultiQube System</div>
                          <div className="grid grid-cols-1 gap-2">
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Asciugatura rapida ed efficiente' : lang === 'fr' ? 'Séchage rapide et efficace' : 'Schnelle und effiziente Trocknung'}</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Prevenzione muffa e danni strutturali' : lang === 'fr' ? 'Prévention des moisissures et des dommages structurels' : 'Schimmel- und Strukturschadenprävention'}</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Tecnologia avanzata di deumidificazione' : lang === 'fr' ? 'Technologie avancée de déshumidification' : 'Fortschrittliche Entfeuchtungstechnologie'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {product.id === 3 && (
                      <div className="flex flex-col items-center">
                        <div className="w-28 h-28 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                          <FaShieldAlt className="w-14 h-14 text-blue-700" />
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-gray-900 text-xl mb-3">{lang === 'it' ? 'Coloranti di marcatura' : lang === 'fr' ? 'Colorants de marquage' : 'Markierungsfarbstoffe'}</div>
                          <div className="grid grid-cols-1 gap-2">
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Localizzazione precisa delle perdite' : lang === 'fr' ? 'Localisation précise des fuites' : 'Präzise Leckortung'}</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Identificazione rapida delle infiltrazioni' : lang === 'fr' ? 'Identification rapide des infiltrations' : 'Schnelle Identifizierung von Undichtigkeiten'}</div>
                            </div>
                            <div className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                              <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                              <div className="text-sm text-gray-800">{lang === 'it' ? 'Soluzione non invasiva' : lang === 'fr' ? 'Solution non invasive' : 'Nichtinvasive Lösung'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Fade>
            ))}
          </div>

          <Fade>
            <div className="mt-24 bg-gradient-to-r from-blue-100 to-blue-100 rounded-2xl p-10 text-center shadow-xl border border-blue-200">
              <p className="text-xl text-gray-800 font-light italic relative">
                <span className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-5xl text-blue-200">&ldquo;</span>
                {data.trotec.conclusion}
                <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-5xl text-blue-200">&rdquo;</span>
              </p>
            </div>
          </Fade>
        </div>
      </section>

      {/* Sezione Certificazioni Professionali */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Fade>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {lang === 'it' ? 'Certificazioni Professionali' :
                 lang === 'fr' ? 'Certifications Professionnelles' :
                 'Professionelle Zertifizierung'}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {lang === 'it' ? 'Il nostro team è certificato per la gestione e il risanamento dei danni causati dall\'acqua, garantendo interventi professionali di alta qualità.' :
                 lang === 'fr' ? 'Notre équipe est certifiée pour la gestion et la réhabilitation des dégâts causés par l\'eau, garantissant des interventions professionnelles de haute qualité.' :
                 'Unser Team ist für die Verwaltung und Sanierung von Wasserschäden zertifiziert und garantiert professionelle Eingriffe von hoher Qualität.'}
              </p>
            </div>
          </Fade>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Certificato 1 */}
            <Fade key="certificato-1">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                <div className="flex flex-col lg:flex-row">
                  <div className="flex-1 p-8">
                    <div className="flex items-start gap-3 mb-4">
                      <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <FaShieldAlt className="text-white text-lg" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 leading-tight">
                          {lang === 'it' ? 'Certificato Fachberater Wasserschadensanierung' :
                           lang === 'fr' ? 'Certificat Fachberater Wasserschadensanierung' :
                           'Zertifikat Fachberater Wasserschadensanierung'}
                        </h3>
                        <p className="text-blue-600 font-medium text-sm mt-1">
                          {lang === 'it' ? 'Rilasciato da Dantherm Academy' :
                           lang === 'fr' ? 'Délivré par Dantherm Academy' :
                           'Ausgestellt von der Dantherm Academy'}
                        </p>
                      </div>
                    </div>
                    <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                      {lang === 'it' ? 'Questo certificato attesta la nostra competenza specialistica nel trattamento dei danni da acqua, garantendo interventi professionali secondo gli standard più elevati del settore.' :
                       lang === 'fr' ? 'Ce certificat atteste notre expertise spécialisée dans le traitement des dégâts d\'eau, garantissant des interventions professionnelles selon les standards les plus élevés du secteur.' :
                       'Dieses Zertifikat bescheinigt unsere Fachkompetenz in der Behandlung von Wasserschäden und gewährleistet professionelle Eingriffe nach den höchsten Branchenstandards.'}
                    </p>
                    <a
                      href="/documents/certificato.pdf"
                      target="_blank"
                      className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                      {lang === 'it' ? 'Visualizza certificato' :
                       lang === 'fr' ? 'Voir le certificat' :
                       'Zertifikat ansehen'}
                    </a>
                  </div>
                  <div className="lg:w-48 flex-shrink-0">
                    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 p-4 flex items-center justify-center">
                      <a
                        href="/documents/certificato.pdf"
                        target="_blank"
                        className="relative group cursor-pointer block"
                      >
                        <Image
                          src="/images/certificates/certificato.png"
                          alt="Certificato Wasserschadensanierung"
                          width={160}
                          height={220}
                          className="rounded-lg shadow-md group-hover:shadow-lg transition-shadow duration-300 border border-gray-200"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-white rounded-full p-2 shadow-lg">
                              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </Fade>

            {/* Certificato 2 */}
            <Fade key="certificato-2">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                <div className="flex flex-col lg:flex-row">
                  <div className="flex-1 p-8">
                    <div className="flex items-start gap-3 mb-4">
                      <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <FaBuilding className="text-white text-lg" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 leading-tight">
                          {lang === 'it' ? 'Certificato Bautrocknungen' :
                           lang === 'fr' ? 'Certificat Séchage des Bâtiments' :
                           'Zertifikat Bautrocknungen'}
                        </h3>
                        <p className="text-green-600 font-medium text-sm mt-1">
                          {lang === 'it' ? 'Certificazione specializzata per servizi di asciugatura edilizia' :
                           lang === 'fr' ? 'Certification spécialisée pour les services de séchage des bâtiments' :
                           'Spezialisierte Zertifizierung für Bautrocknung-Dienstleistungen'}
                        </p>
                      </div>
                    </div>
                    <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                      {lang === 'it' ? 'Questo certificato attesta la nostra competenza specialistica nei servizi di asciugatura edilizia, garantendo interventi professionali per il trattamento dell\'umidità e l\'asciugatura delle strutture edilizie.' :
                       lang === 'fr' ? 'Ce certificat atteste notre expertise spécialisée dans les services de séchage des bâtiments, garantissant des interventions professionnelles pour le traitement de l\'humidité et le séchage des structures de construction.' :
                       'Dieses Zertifikat bescheinigt unsere Fachkompetenz in Bautrocknungen-Dienstleistungen und gewährleistet professionelle Eingriffe zur Behandlung von Feuchtigkeit und Trocknung von Baustrukturen.'}
                    </p>
                    <a
                      href="/images/certificates/certificato-2.pdf"
                      target="_blank"
                      className="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                      {lang === 'it' ? 'Visualizza certificato asciugatura' :
                       lang === 'fr' ? 'Voir le certificat de séchage' :
                       'Bautrocknungen-Zertifikat ansehen'}
                    </a>
                  </div>
                  <div className="lg:w-48 flex-shrink-0">
                    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 p-4 flex items-center justify-center">
                      <a
                        href="/images/certificates/certificato-2.pdf"
                        target="_blank"
                        className="relative group cursor-pointer block"
                      >
                        <Image
                          src="/images/certificates/certificato-2.png"
                          alt="Certificato Bautrocknungen"
                          width={160}
                          height={220}
                          className="rounded-lg shadow-md group-hover:shadow-lg transition-shadow duration-300 border border-gray-200"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-white rounded-full p-2 shadow-lg">
                              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </Fade>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-16 lg:grid-cols-2 items-center">
            <Fade>
              <div>
                <div className="mb-8">
                  <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                    {data.contact_form.title}
                  </h2>
                  <p className="mt-4 text-base leading-8 text-gray-600">
                    {data.contact_form.description}
                  </p>
                </div>
                
                <div className="rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 text-white p-8 shadow-lg">
                  <h3 className="text-xl font-semibold mb-4">{lang === 'it' ? 'Chiama Subito' : lang === 'fr' ? 'Appelez Maintenant' : 'Rufen Sie Sofort An'}</h3>
                  <p className="mb-6">{lang === 'it' ? 'Per emergenze immediate, contattaci telefonicamente per ricevere assistenza immediata.' : 
                    lang === 'fr' ? 'Pour les urgences immédiates, contactez-nous par téléphone pour une assistance immédiate.' : 
                    'Für sofortige Notfälle, kontaktieren Sie uns telefonisch, um sofortige Hilfe zu erhalten.'}</p>
                  <Link 
                    href={`tel:${data.cta.emergency_number}`}
                    className="inline-flex items-center text-lg font-semibold hover:underline"
                  >
                    <FaPhone className="mr-2" />
                    {data.cta.emergency_number}
                  </Link>
                </div>
              </div>
            </Fade>
            
            <Fade>
              <div>
                <EmergencyContactForm t={t} lang={lang} />
              </div>
            </Fade>
          </div>
        </div>
      </section>
    </main>
  );
}
