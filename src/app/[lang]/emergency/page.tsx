import { getTranslation } from "@/i18n/server";
import { emergencyServices } from "@/mocks/emergency";
import { EmergencyServerComponent } from "@/components/emergency/EmergencyServerComponent";

interface EmergencyPageProps {
  params: Promise<{ lang: string }>;
}

// Questa è ora una pagina server
export default async function EmergencyPage({ params }: EmergencyPageProps) {
  // Attendiamo che i parametri siano disponibili
  const resolvedParams = await params;
  const lang = resolvedParams.lang;

  // Otteniamo i dati di traduzione lato server
  const translation = await getTranslation(lang);

  // Otteniamo i dati dei servizi di emergenza
  const emergencyData = emergencyServices[lang as keyof typeof emergencyServices] || emergencyServices.it;

  // Renderizziamo il componente server con i dati ottenuti dal server
  return <EmergencyServerComponent data={emergencyData} t={translation} lang={lang} />;
}
